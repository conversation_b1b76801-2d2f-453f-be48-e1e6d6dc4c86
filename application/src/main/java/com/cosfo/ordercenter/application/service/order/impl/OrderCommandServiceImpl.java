package com.cosfo.ordercenter.application.service.order.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.ordercenter.application.inbound.provider.delivery.converter.OrderDeliveryFeeSnapshotConverter;
import com.cosfo.ordercenter.application.inbound.provider.order.converter.*;
import com.cosfo.ordercenter.application.service.aftersale.NotifyBizService;
import com.cosfo.ordercenter.application.service.aftersale.OrderAfterSaleBizService;
import com.cosfo.ordercenter.application.service.order.OrderCommandService;
import com.cosfo.ordercenter.client.common.*;
import com.cosfo.ordercenter.client.req.*;
import com.cosfo.ordercenter.client.req.event.*;
import com.cosfo.ordercenter.client.resp.OrderAddressDTO;
import com.cosfo.ordercenter.client.resp.OrderDTO;
import com.cosfo.ordercenter.client.resp.OrderItemAfterSaleRuleDTO;
import com.cosfo.ordercenter.client.resp.delivery.MerchantDeliveryFeeSnapshotDTO;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.common.constants.DeliveryConstant;
import com.cosfo.ordercenter.domain.aftersale.entity.OrderAfterSaleEntity;
import com.cosfo.ordercenter.domain.aftersale.param.OrderAfterSaleListQueryParam;
import com.cosfo.ordercenter.domain.aftersale.repository.OrderAfterSaleQueryRepository;
import com.cosfo.ordercenter.domain.delivery.entity.OrderDeliveryFeeSnapshotEntity;
import com.cosfo.ordercenter.domain.delivery.service.OrderDeliveryFeeSnapshotCommandDomainService;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemSnapshotEntity;
import com.cosfo.ordercenter.domain.order.entity.OrderItemWithSnapshotEntity;
import com.cosfo.ordercenter.domain.order.param.command.*;
import com.cosfo.ordercenter.domain.order.repository.OrderItemQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderItemSnapshotQueryRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.cosfo.ordercenter.domain.order.service.*;
import com.cosfo.ordercenter.facade.TenantQueryFacade;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.i18n.exception.I18nBizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderCommandServiceImpl implements OrderCommandService {

    @Resource
    private OrderAfterSaleBizService orderAfterSaleBizService;

    @Resource
    private OrderCommandDomainService orderCommandDomainService;
    @Resource
    private OrderItemCommandDomainService orderItemCommandDomainService;
    @Resource
    private OrderAddressCommandDomainService addressCommandDomainService;
    @Resource
    private OrderDeliveryFeeSnapshotCommandDomainService deliveryFeeSnapshotCommandDomainService;
    @Resource
    private OrderItemSnapshotCommandDomainService orderItemSnapshotCommandDomainService;
    @Resource
    private OrderCombineSnapshotCommandDomainService combineSnapshotCommandDomainService;
    @Resource
    private OrderItemExtraCommandDomainService orderItemExtraCommandDomainService;
    @Resource
    private OrderItemCommandDomainService orderItemCommandRepository;

    @Resource
    private OrderQueryRepository orderQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private OrderAfterSaleQueryRepository orderAfterSaleQueryRepository;
    @Resource
    private OrderItemSnapshotQueryRepository orderItemSnapshotQueryRepository;

    @Resource
    private TenantQueryFacade tenantQueryFacade;
    @Resource
    private NotifyBizService notifyBizService;


    @Override
    public Boolean updateById(OrderCommandParam orderCommandParam) {
        return orderCommandDomainService.updateById(orderCommandParam);
    }

    @Override
    public Boolean updatePayType(OrderCommandParam orderCommandParam) {
        return orderCommandDomainService.updatePayType(orderCommandParam);
    }

    @Override
    public Boolean updateStatus(OrderStatusUpdateReq orderStatusUpdateReq) {
        OrderStatusUpdateParam orderStatusUpdateParam = OrderParamConverter.convertToOrderStatusUpdateParam(orderStatusUpdateReq);
        return orderCommandDomainService.updateStatus(orderStatusUpdateParam);
    }

    @Override
    public Integer batchUpdateStatus(OrderStatusBatchUpdateReq orderStatusBatchUpdateReq) {
        OrderStatusBatchUpdateParam orderStatusBatchUpdateParam = OrderParamConverter.convertToOrderStatusBatchUpdateParam(orderStatusBatchUpdateReq);
        return orderCommandDomainService.batchUpdateStatus(orderStatusBatchUpdateParam);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean selfLifting(OrderSelfLiftReq orderSelfLiftReq) {
        List<OrderItemWithSnapshotEntity> data = orderItemQueryRepository.queryItemWithSnapshotByOrderId(orderSelfLiftReq.getOrderId());
        List<OrderItemCommandParam> updateList = data.stream().map(snapshotDTO -> {
            OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = JSON.parseObject(snapshotDTO.getAfterSaleRule(), OrderItemAfterSaleRuleDTO.class);
            OrderItemCommandParam orderItem = new OrderItemCommandParam();
            orderItem.setId(snapshotDTO.getOrderItemId());
            orderItem.setAfterSaleExpiryTime(LocalDateTime.now().plusHours(orderItemAfterSaleRuleDTO.getApplyEndTime()));
            return orderItem;
        }).collect(Collectors.toList());
        OrderSelfLiftParam orderSelfLiftParam = OrderParamConverter.convertToOrderSelfLiftParam(orderSelfLiftReq);
        return orderItemCommandDomainService.batchUpdateAfterSaleExpiryTime(updateList) && orderCommandDomainService.selfLifting(orderSelfLiftParam);
    }

    @Override
    public Boolean batchUpdateProfitSharingFinishTime(ProfitSharingFinishTimeReq req) {
        List<OrderCommandParam> updateList = new ArrayList<>();
        for (ProfitSharingFinishTimeReq.ProfitSharingFinishTimeOrder profitSharingFinishTimeOrder : req.getProfitSharingFinishTimeOrderList()) {
            OrderCommandParam order = new OrderCommandParam();
            order.setId(profitSharingFinishTimeOrder.getOrderId());
            order.setProfitSharingFinishTime(profitSharingFinishTimeOrder.getProfitSharingFinishTime());
            updateList.add(order);
        }
        return orderCommandDomainService.batchUpdateProfitSharingFinishTime(updateList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean close(OrderCloseReq req) {
        OrderEntity order = queryOrderId(req);
        if (order == null) {
            throw new BizException("该订单不存在");
        }
        List<OrderEntity> needCloseOrder = Lists.newArrayList(order);
        boolean isCombineOrder = OrderTypeEnum.COMBINE_ORDER.getValue().equals(order.getOrderType());
        if (isCombineOrder) {
            Long combineOrderId = order.getCombineOrderId();
            List<OrderEntity> orders = orderQueryRepository.queryByCombineId(combineOrderId, order.getTenantId());
            needCloseOrder = orders;
        }
        for (OrderEntity closeOrderDTO : needCloseOrder) {
            if (!OrderStatusEnum.ableApplyNotSendAfterSale(closeOrderDTO.getStatus())) {
                throw new BizException(isCombineOrder ? "组合包订单存在已配送订单，不能发起关单" : "订单存在已配送订单，不能发起关单");
            }
        }
        // 查询售后单
        List<OrderAfterSaleEntity> afterSaleList = getOrderAfterSaleDTOS(needCloseOrder);
        if (!CollectionUtils.isEmpty(afterSaleList)) {
            throw new BizException("该订单存在未处理售后单，请先处理售后单", 200, "CLOSE_ORDER_EXIST_AFTER_SALES");
        }

        List<OrderEntity> finalNeedCloseOrder = needCloseOrder;
        // 更新订单状态为关单中
        orderCommandDomainService.updateOrderForClose(finalNeedCloseOrder);
        //创建售后单
        orderAfterSaleBizService.createAfterSaleOrderForClose(finalNeedCloseOrder, req);
        //自动审核
        orderAfterSaleBizService.autoReviewSubmissionsForClose(finalNeedCloseOrder);
        return true;
    }

    private List<OrderAfterSaleEntity> getOrderAfterSaleDTOS(List<OrderEntity> needCloseOrder) {
        OrderAfterSaleListQueryParam queryReq = new OrderAfterSaleListQueryParam();
        queryReq.setOrderIds(needCloseOrder.stream().map(OrderEntity::getId).collect(Collectors.toList()));
        queryReq.setStatusList(Lists.newArrayList(OrderAfterSaleStatusEnum.UNAUDITED.getValue(), OrderAfterSaleStatusEnum.WAIT_CONFIRM.getValue()));
        List<OrderAfterSaleEntity> afterSaleList = orderAfterSaleQueryRepository.queryList(queryReq);
        return afterSaleList;
    }

    private OrderEntity queryOrderId(OrderCloseReq orderCloseReq) {
        if (orderCloseReq.getOrderId() != null) {
            return orderQueryRepository.queryById(orderCloseReq.getOrderId());
        }
        // 查询组合单
        if (!StringUtils.isEmpty(orderCloseReq.getOrderNo())) {
            List<OrderEntity> orders = orderQueryRepository.queryByOrderNos(Lists.newArrayList(orderCloseReq.getOrderNo()));
            return CollectionUtils.isEmpty(orders) ? null : orders.get(0);
        }
        return null;
    }


    @Override
    public Boolean auditSuccess(OrderAuditReq req) {
        Boolean result = orderCommandDomainService.auditSuccess(OrderParamConverter.convertToOrderAuditCommandParam(req));
        if(Boolean.FALSE.equals(result)){
            log.warn("订单审核状态更新失败, req:{}", JSON.toJSONString(req));
            throw new BizException("订单审核状态更新失败");
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long create(OrderCreateReq req) {
        OrderDTO orderDTO = req.getOrderDTO();
        OrderEntity orderEntity = OrderConverter.convertToEntity(orderDTO);
        Long orderId = orderCommandDomainService.saveOrder(orderEntity);
        List<OrderItemEntity> orderItems = handleOrderItem(req.getOrderItemList(), orderEntity);
        handleOrderItemSnapshot(req.getOrderItemList(), orderItems, orderEntity);
        handleDeliverySnapshot(req.getDeliveryFeeSnapshotDTO(), orderEntity);
        handleOrderAddress(req.getOrderAddressDTO(), orderEntity);
        // 开放平台-外部下单
        if (!StringUtils.isEmpty(orderDTO.getCustomerOrderId()) && OrderSourceEnum.OPENAPI.getValue().equals(orderDTO.getOrderSource())) {
            handleOrderItemExtra(req.getOrderItemList(), orderItems);
        }
        return orderId;
    }


    private void handleOrderAddress(OrderAddressDTO addressDTO, OrderEntity order) {
        OrderAddressCommandParam orderAddress = OrderAddressConverter.convertToCommandParam(addressDTO);
        orderAddress.setId(null);
        orderAddress.setOrderId(order.getId());
        addressCommandDomainService.add(orderAddress);
    }

    private void handleDeliverySnapshot(MerchantDeliveryFeeSnapshotDTO deliveryFeeSnapshotDTO, OrderEntity order) {
        if (deliveryFeeSnapshotDTO == null) {
            return;
        }
        OrderDeliveryFeeSnapshotEntity deliveryFeeSnapshot = OrderDeliveryFeeSnapshotConverter.convertToEntity(deliveryFeeSnapshotDTO);
        deliveryFeeSnapshot.setId(null);
        deliveryFeeSnapshot.setOrderId(order.getId());
        deliveryFeeSnapshotCommandDomainService.save(deliveryFeeSnapshot);
    }

    private void handleOrderItemSnapshot(List<OrderItemCreateReq> orderItemCreateReqs, List<OrderItemEntity> collect, OrderEntity order) {
        List<OrderItemSnapshotAddParam> snapshotList = new ArrayList<>();
        Map<Long, OrderItemCreateReq> itemCreateReqMap = orderItemCreateReqs.stream().collect(Collectors.toMap(OrderItemCreateReq::getItemId, o -> o));
        List<OrderCombineSnapshotAddParam> combineSnapshots = new ArrayList<>();
        collect.forEach(orderItem -> {
            OrderItemCreateReq orderItemCreateReq = itemCreateReqMap.get(orderItem.getItemId());
            OrderItemSnapshotAddParam orderItemSnapshot = OrderItemSnapshotConverter.convertToAddParam(orderItemCreateReq);
            orderItemSnapshot.setTenantId(orderItem.getTenantId());
            orderItemSnapshot.setOrderItemId(orderItem.getId());
            orderItemSnapshot.setMainPicture(Objects.isNull(orderItemCreateReq.getMainPicture()) ? null : orderItemCreateReq.getMainPicture().split(",")[0]);
            orderItemSnapshot.setAfterSaleRule(JSON.toJSONString(orderItemCreateReq.getOrderAfterSaleRule()));
            orderItemSnapshot.setOrderId(orderItem.getOrderId());
            if (Objects.equals(order.getOrderType(), OrderTypeEnum.COMBINE_ORDER.getValue())) {
                OrderCombineSnapshotAddParam combineSnapshot = new OrderCombineSnapshotAddParam();
                combineSnapshot.setTenantId(order.getTenantId());
                combineSnapshot.setCombineOrderId(order.getCombineOrderId());
                combineSnapshot.setCombineItemId(orderItemCreateReq.getCombineItemId());
                combineSnapshot.setItemId(orderItemCreateReq.getItemId());
                OrderCombineItemCreateReq orderCombineItemCreateReq = orderItemCreateReq.getOrderCombineItemCreateReq();
                combineSnapshot.setQuantity(orderCombineItemCreateReq.getQuantity());
                combineSnapshot.setOriginalPrice(orderCombineItemCreateReq.getOriginalPrice());
                combineSnapshot.setOrderItemId(orderItem.getId());
                combineSnapshots.add(combineSnapshot);
            }
            snapshotList.add(orderItemSnapshot);
        });
        // batch save snapshot
        orderItemSnapshotCommandDomainService.batchSave(snapshotList);
        //
        if (Objects.equals(order.getOrderType(), OrderTypeEnum.COMBINE_ORDER.getValue())) {
            combineSnapshotCommandDomainService.batchSave(combineSnapshots);
        }
    }


    private void handleOrderItemExtra(List<OrderItemCreateReq> orderItemCreateReqs, List<OrderItemEntity> collect) {
        List<OrderItemExtraAddParam> orderItemExtraList = new ArrayList<>();
        Map<Long, OrderItemCreateReq> itemCreateReqMap = orderItemCreateReqs.stream().collect(Collectors.toMap(OrderItemCreateReq::getItemId, o -> o));
        collect.forEach(orderItem -> {
            OrderItemCreateReq orderItemCreateReq = itemCreateReqMap.get(orderItem.getItemId());
            OrderItemExtraAddParam orderItemExtra = new OrderItemExtraAddParam();
            orderItemExtra.setTenantId(orderItem.getTenantId());
            orderItemExtra.setOrderId(orderItem.getOrderId());
            orderItemExtra.setOrderItemId(orderItem.getId());
            orderItemExtra.setCustomerOrderItemId(orderItemCreateReq.getCustomerOrderItemId());
            orderItemExtra.setCustomerSkuCode(orderItemCreateReq.getCustomerSkuCode());
            orderItemExtra.setCustomerSkuTitle(orderItemCreateReq.getCustomerSkuTitle());
            orderItemExtra.setCustomerSkuSpecification(orderItemCreateReq.getCustomerSkuSpecification());
            orderItemExtra.setCustomerSkuSpecificationUnit(orderItemCreateReq.getCustomerSkuSpecificationUnit());
            orderItemExtra.setSkuCode(orderItemCreateReq.getSupplySku());

            orderItemExtraList.add(orderItemExtra);
        });
        // batch save
        orderItemExtraCommandDomainService.batchSave(orderItemExtraList);
    }

    private List<OrderItemEntity> handleOrderItem(List<OrderItemCreateReq> orderItemCreateReqs, OrderEntity order) {
        List<OrderItemCommandParam> collect = orderItemCreateReqs.stream().map(orderItemDTO -> {
            OrderItemCommandParam orderItem = new OrderItemCommandParam();
            orderItem.setItemId(orderItemDTO.getItemId());
            orderItem.setAmount(orderItemDTO.getAmount());
            orderItem.setTenantId(order.getTenantId());
            orderItem.setOrderId(order.getId());
            orderItem.setOrderType(order.getOrderType());
            orderItem.setPayablePrice(orderItemDTO.getPrice());
            if (order.getWarehouseNo() != null) {
                orderItem.setStoreNo(Integer.valueOf(order.getWarehouseNo()));
            }
            BigDecimal totalPrice = NumberUtil.mul(orderItemDTO.getPrice(), orderItemDTO.getAmount());
            if (WarehouseTypeEnum.PROPRIETARY.getCode().equals(order.getWarehouseType())) {
                orderItem.setStatus(OrderItemStatusEnum.NO_PAYMENT.getCode());
            } else {
                orderItem.setStatus(OrderItemStatusEnum.CREATING_ORDER.getCode());
            }
            orderItem.setTotalPrice(totalPrice);
            return orderItem;
        }).collect(Collectors.toList());
        return orderItemCommandRepository.batchSave(collect);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancel(OrderCancelReq req) {
        OrderStatusBatchUpdateParam updateReq = new OrderStatusBatchUpdateParam();
        updateReq.setOrderIds(req.getOrderIds());
        updateReq.setTenantId(req.getTenantId());
        updateReq.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.CREATING_ORDER.getCode(), OrderStatusEnum.NO_PAYMENT.getCode()));
        updateReq.setUpdateStatus(OrderStatusEnum.CANCELED.getCode());
        boolean result = orderCommandDomainService.batchUpdateStatus(updateReq) == req.getOrderIds().size();
        log.info("取消订单，订单状态更新结果={}", result);

        OrderItemStatusBatchUpdateParam updateParam = new OrderItemStatusBatchUpdateParam();
        updateParam.setStatus(OrderItemStatusEnum.CANCELED.getCode());
        updateParam.setTenantId(req.getTenantId());
        updateParam.setOrderIds(req.getOrderIds());
        result = result && orderItemCommandRepository.batchUpdateStatus(updateParam);
        log.info("取消订单，订单项状态更新结果={}", result);

        if (!result) {
            throw new ProviderException("订单取消失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean lockStockSuccess(LockStockSuccessReq req) {

        OrderStatusUpdateParam updateReq = new OrderStatusUpdateParam();
        updateReq.setOrderId(req.getOrderId());
        updateReq.setTenantId(req.getTenantId());
        updateReq.setDeliveryTime(req.getDeliveryTime());
        updateReq.setOriginStatus(OrderStatusEnum.CREATING_ORDER.getCode());
        updateReq.setStatus(OrderStatusEnum.NO_PAYMENT.getCode());
        boolean result = orderCommandDomainService.updateStatus(updateReq);

        log.info("锁定库存成功, 订单状态更新结果={}", result);

        OrderItemStatusBatchUpdateParam updateParam = new OrderItemStatusBatchUpdateParam();
        updateParam.setOrderIds(Lists.newArrayList(req.getOrderId()));
        updateParam.setStoreNo(req.getStoreNo());
        updateParam.setTenantId(req.getTenantId());
        updateParam.setStatus(OrderItemStatusEnum.NO_PAYMENT.getCode());
        result = result && orderItemCommandRepository.batchUpdateStatus(updateParam);

        log.info("锁定库存成功, 订单项状态更新结果={}", result);

        if (!result) {
            throw new BizException("订单状态更新失败");
        }
        return true;

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirm(OrderConfirmReq req) {
        OrderStatusUpdateParam updateReq = new OrderStatusUpdateParam();
        updateReq.setStatus(OrderStatusEnum.FINISHED.getCode());
        updateReq.setOrderId(req.getOrderId());
        updateReq.setTenantId(req.getTenantId());
        boolean result = orderCommandDomainService.updateStatus(updateReq);
        log.info("确认收货, 订单状态更新结果={}", result);
        List<OrderItemEntity> orderItems = orderItemQueryRepository.queryByOrderId(req.getOrderId());
        List<Long> orderItemIds = orderItems.stream().map(OrderItemEntity::getId).collect(Collectors.toList());
        // 查询订单快照
        List<OrderItemSnapshotEntity> orderItemSnapshots = orderItemSnapshotQueryRepository.queryByOrderItemIds(orderItemIds);
        Map<Long, String> ruleMap = orderItemSnapshots.stream()
                .collect(Collectors.toMap(OrderItemSnapshotEntity::getOrderItemId, OrderItemSnapshotEntity::getAfterSaleRule));
        // 更新可申请售后时间
        for (OrderItemEntity orderItem : orderItems) {
            String rule = ruleMap.get(orderItem.getId());
            OrderItemAfterSaleRuleDTO orderItemAfterSaleRuleDTO = JSON.parseObject(rule, OrderItemAfterSaleRuleDTO.class);
            LocalDateTime afterSaleExpiryTime = LocalDateTime.now().plusHours(orderItemAfterSaleRuleDTO.getApplyEndTime());
            OrderItemUpdateParam orderItemUpdateReq = new OrderItemUpdateParam();
            orderItemUpdateReq.setOrderItemId(orderItem.getId());
            orderItemUpdateReq.setAfterSaleExpiryTime(afterSaleExpiryTime);
            result = result && orderItemCommandRepository.updateAfterSaleExpiryTime(orderItemUpdateReq);
            log.info("确认收货，订单项={}更新结果={}", orderItem.getId(), result);
        }
        if (!result) {
            throw new BizException("订单状态更新失败");
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean fulfillmentOrderCreate(OrderFulfillmentOrderCreateReq req) {
        OrderDeliveryUpdateParam updateParam = new OrderDeliveryUpdateParam();
        updateParam.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_AUDIT.getCode(), OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode()));
        updateParam.setOrderId(req.getOrderId());
        updateParam.setDeliveryTime(req.getDeliveryTime());
        updateParam.setFulfillmentNo(req.getFulfillmentNo());
        boolean result = orderCommandDomainService.updateDeliveryTime(updateParam);
        log.info("履约单创建, 订单项状态更新结果={}", result);

        if (req.getStoreNo() != null) {
            OrderItemStatusBatchUpdateParam statusBatchUpdateParam = new OrderItemStatusBatchUpdateParam();
            statusBatchUpdateParam.setOrderIds(Lists.newArrayList(req.getOrderId()));
            statusBatchUpdateParam.setStoreNo(req.getStoreNo());
            statusBatchUpdateParam.setStatus(OrderItemStatusEnum.PAID.getCode());
            result = result && orderItemCommandRepository.batchUpdateStatus(statusBatchUpdateParam);
        }

        if (!result) {
            throw new ProviderException("订单更新失败");
        }
        return true;
    }

    @Override
    public Boolean finish() {
        // 接口废弃
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean paySuccess(OrderPaySuccessReq req) {
        Integer targetStatus = OrderStatusEnum.WAITING_DELIVERY.getCode();
        OrderEntity order = orderQueryRepository.queryById(req.getOrderId());
        if(PayTypeEnum.OFFLINE_PAY.getCode ().equals(order.getPayType ()) && !order.getOrderType ().equals (OrderTypeEnum.PRESALE_ORDER.getValue ())){
            targetStatus = OrderStatusEnum.WAIT_AUDIT.getCode ();
        }else {
            // 不是三方仓订单，查询是否需要审核订单
            if (!WarehouseTypeEnum.THREE_PARTIES.getCode ().equals (order.getWarehouseType ())) {
                boolean needAuditFlag = tenantQueryFacade.getOrderAuditSwitch (order.getStoreId ());
                targetStatus = needAuditFlag ? OrderStatusEnum.WAIT_AUDIT.getCode () : OrderStatusEnum.WAITING_DELIVERY.getCode ();
            } else if (WarehouseTypeEnum.THREE_PARTIES.getCode ().equals (order.getWarehouseType ()) && OrderTypeEnum.PRESALE_ORDER.getValue ().equals (order.getOrderType ())) {
                targetStatus = OrderStatusEnum.WAIT_DELIVERY.getCode ();
            }
        }

        OrderStatusUpdateParam updateReq = new OrderStatusUpdateParam();
        updateReq.setOrderId(req.getOrderId());
        updateReq.setOriginStatus(OrderStatusEnum.NO_PAYMENT.getCode());
        updateReq.setStatus(targetStatus);
        updateReq.setBeginDeliveryTime(order.getBeginDeliveryTime());
        boolean result = orderCommandDomainService.updateStatus(updateReq);

        log.info("订单支付成功, 订单状态更新结果={}, orderNo={}", result, order.getOrderNo());

        OrderItemStatusBatchUpdateParam batchUpdateParam = new OrderItemStatusBatchUpdateParam();
        batchUpdateParam.setOrderIds(Lists.newArrayList(req.getOrderId()));
        batchUpdateParam.setStatus(OrderItemStatusEnum.PAID.getCode());
        result = result && orderItemCommandRepository.batchUpdateStatus(batchUpdateParam);

        log.info("订单支付成功, 订单项状态更新结果={}, orderNo={}", result, order.getOrderNo());

        if (!result) {
            order = orderQueryRepository.queryById(req.getOrderId());
            log.error("订单支付成功，更新订单状态失败 order={}", JSON.toJSONString(order));
            throw new I18nBizException("订单支付成功，更新订单状态失败，orderNo={0}", order.getOrderNo());
        }
        return true;
    }

    @Override
    public Integer updateOrderDeliveryTime(OrderUpdateDelivertDateReq req) {
        List<OrderEntity> sourceOrderList = orderQueryRepository.queryByOrderNos(req.getOrderNoList());
        Integer count = orderCommandDomainService.updateOrderDeliveryTime(req.getOrderNoList(), req.getDeliveryDate().atStartOfDay());
        notifyBizService.updateOrderDeliveryTimeSmsNotify(sourceOrderList, req.getDeliveryDate());
        log.info("订单列表{},数量{},更新配送时间为[{}],更新数量{}", req.getOrderNoList(), req.getOrderNoList().size(), req.getDeliveryDate(), count);
        return count;
    }

    @Override
    public Boolean setDeliveryDatePresaleOrder(OrderPresaleSetDeliveryDateReq req) {
        OrderEntity orderEntity = orderQueryRepository.queryByNo(req.getOrderNo());
        if (orderEntity == null) {
            throw new I18nBizException("orderNo={0}, 该订单不存在", req.getOrderNo());
        }

        log.info("预售订单设置配送日期, order={}", JSON.toJSONString(orderEntity));

        // 不是预售订单
        if(!OrderTypeEnum.PRESALE_ORDER.getValue().equals(orderEntity.getOrderType())){
            throw new I18nBizException("orderNo={0}, 该订单不是预售订单类型", req.getOrderNo());
        }

        boolean result = orderCommandDomainService.setDeliveryDatePresaleOrder(orderEntity.getId(), req.getDeliveryDate().atStartOfDay());
        if(!result){
            throw new I18nBizException("预售订单设置配送日期，更新失败，状态非已支付，orderNo={0}", req.getOrderNo());
        }

        return true;
    }

    @Override
    public Boolean selfLiftingFinish(OrderSelfLiftingFinishReq req) {
        OrderSelfLiftingFinishParam orderSelfLiftingFinishParam = OrderConverter.convertToFinishParam(req);
        return orderCommandDomainService.selfLiftingFinish(orderSelfLiftingFinishParam);
    }

    @Override
    public Boolean updateOrderStoreNo(OrderUpdateStoreNoReq req) {
        List<OrderEntity> orderList = orderQueryRepository.queryByOrderNos(Collections.singletonList(req.getOrderNo()));
        if (CollectionUtils.isEmpty(orderList)) {
            throw new BizException("该订单不存在");
        }
        return orderItemCommandRepository.updateStoreNo(orderList.get(0).getId(), req.getSourceStoreNo(), req.getStoreNo());
    }

    @Transactional
    @Override
    public OrderResp refreshOrderAmount(RefreshOrderAmountReq req) {
        // TODO: 临时硬编码手续费参数，后续从req中获取
        BigDecimal newServiceFee = BigDecimal.valueOf(5.00); // 硬编码新手续费
        BigDecimal oriServiceFee = BigDecimal.valueOf(3.00); // 硬编码原手续费

        OrderEntity order;

        // 判断是否需要更新手续费（这里先用硬编码逻辑判断，后续改为从请求参数判断）
        boolean needUpdateServiceFee = true; // 临时硬编码为true，后续根据请求参数判断

        if (needUpdateServiceFee) {
            log.info("同时刷新运费和手续费，订单ID：{}，原运费：{}，新运费：{}，原手续费：{}，新手续费：{}",
                    req.getOrderId(), req.getOriDeliveryFee(), req.getDeliveryFeeSnapshotDTO().getDeliveryFee(),
                    oriServiceFee, newServiceFee);
            // 同时更新运费和手续费
            order = orderCommandDomainService.updateDeliveryFeeAndServiceFee(
                    req.getOrderId(),
                    req.getDeliveryFeeSnapshotDTO().getDeliveryFee(),
                    req.getOriDeliveryFee(),
                    newServiceFee,
                    oriServiceFee
            );
        } else {
            log.info("仅刷新运费，订单ID：{}，原运费：{}，新运费：{}",
                    req.getOrderId(), req.getOriDeliveryFee(), req.getDeliveryFeeSnapshotDTO().getDeliveryFee());
            // 仅更新运费（保持原有逻辑）
            order = orderCommandDomainService.updateDeliveryFee(req.getOrderId(), req.getDeliveryFeeSnapshotDTO().getDeliveryFee(), req.getOriDeliveryFee());
        }

        // 更新原快照
        boolean effective = deliveryFeeSnapshotCommandDomainService.updateEffectiveFlag(req.getOrderId(), DeliveryConstant.SCENE_PLACE_ORDER);
        if (!effective) {
            throw new BizException("更新运费快照失败！");
        }
        // 新增快照
        handleDeliverySnapshot(req.getDeliveryFeeSnapshotDTO(), order);
        return OrderConverter.convertToOrderResp(order);
    }
}
