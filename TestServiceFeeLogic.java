import java.math.BigDecimal;

/**
 * 测试手续费计算逻辑的简单脚本
 */
public class TestServiceFeeLogic {
    
    public static void main(String[] args) {
        // 测试场景1：同时更新运费和手续费
        System.out.println("=== 测试场景1：同时更新运费和手续费 ===");
        testUpdateBothFees();
        
        // 测试场景2：只更新运费
        System.out.println("\n=== 测试场景2：只更新运费 ===");
        testUpdateDeliveryFeeOnly();
        
        // 测试场景3：手续费从0增加
        System.out.println("\n=== 测试场景3：手续费从0增加 ===");
        testServiceFeeFromZero();
    }
    
    private static void testUpdateBothFees() {
        // 模拟订单数据
        BigDecimal originalPayablePrice = BigDecimal.valueOf(100.00);
        BigDecimal originalDeliveryFee = BigDecimal.valueOf(8.00);
        BigDecimal originalServiceFee = BigDecimal.valueOf(3.00);
        
        BigDecimal newDeliveryFee = BigDecimal.valueOf(10.00);
        BigDecimal newServiceFee = BigDecimal.valueOf(5.00);
        
        // 计算新的应付金额
        BigDecimal deliveryFeeDiff = originalDeliveryFee.subtract(newDeliveryFee);
        BigDecimal serviceFeeDiff = originalServiceFee.subtract(newServiceFee);
        BigDecimal newPayablePrice = originalPayablePrice.subtract(deliveryFeeDiff).subtract(serviceFeeDiff);
        
        System.out.println("原应付金额: " + originalPayablePrice);
        System.out.println("原运费: " + originalDeliveryFee + " -> 新运费: " + newDeliveryFee);
        System.out.println("原手续费: " + originalServiceFee + " -> 新手续费: " + newServiceFee);
        System.out.println("运费差额: " + deliveryFeeDiff);
        System.out.println("手续费差额: " + serviceFeeDiff);
        System.out.println("新应付金额: " + newPayablePrice);
        
        // 验证计算
        BigDecimal expected = BigDecimal.valueOf(100.00).subtract(BigDecimal.valueOf(-2.00)).subtract(BigDecimal.valueOf(-2.00));
        System.out.println("预期结果: " + expected);
        System.out.println("计算正确: " + newPayablePrice.equals(expected));
    }
    
    private static void testUpdateDeliveryFeeOnly() {
        BigDecimal originalPayablePrice = BigDecimal.valueOf(100.00);
        BigDecimal originalDeliveryFee = BigDecimal.valueOf(8.00);
        BigDecimal newDeliveryFee = BigDecimal.valueOf(10.00);
        
        BigDecimal deliveryFeeDiff = originalDeliveryFee.subtract(newDeliveryFee);
        BigDecimal newPayablePrice = originalPayablePrice.subtract(deliveryFeeDiff);
        
        System.out.println("原应付金额: " + originalPayablePrice);
        System.out.println("原运费: " + originalDeliveryFee + " -> 新运费: " + newDeliveryFee);
        System.out.println("运费差额: " + deliveryFeeDiff);
        System.out.println("新应付金额: " + newPayablePrice);
        
        BigDecimal expected = BigDecimal.valueOf(102.00);
        System.out.println("预期结果: " + expected);
        System.out.println("计算正确: " + newPayablePrice.equals(expected));
    }
    
    private static void testServiceFeeFromZero() {
        BigDecimal originalPayablePrice = BigDecimal.valueOf(100.00);
        BigDecimal originalServiceFee = BigDecimal.ZERO;
        BigDecimal newServiceFee = BigDecimal.valueOf(5.00);
        
        BigDecimal serviceFeeDiff = originalServiceFee.subtract(newServiceFee);
        BigDecimal newPayablePrice = originalPayablePrice.subtract(serviceFeeDiff);
        
        System.out.println("原应付金额: " + originalPayablePrice);
        System.out.println("原手续费: " + originalServiceFee + " -> 新手续费: " + newServiceFee);
        System.out.println("手续费差额: " + serviceFeeDiff);
        System.out.println("新应付金额: " + newPayablePrice);
        
        BigDecimal expected = BigDecimal.valueOf(105.00);
        System.out.println("预期结果: " + expected);
        System.out.println("计算正确: " + newPayablePrice.equals(expected));
    }
}
