package com.cosfo.ordercenter.domain.order.repository;


import com.cosfo.ordercenter.domain.order.entity.*;
import com.cosfo.ordercenter.domain.order.param.command.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
public interface OrderCommandRepository {
    /**
     * 批量更新订单状态
     *
     * @param orderStatusBatchUpdateParam
     * @return
     */
    Integer batchUpdateStatus(OrderStatusBatchUpdateParam orderStatusBatchUpdateParam);

    /**
     * 按id更新订单
     *
     * @param orderCommandParam
     * @return
     */
    Boolean updateById(OrderCommandParam orderCommandParam);

    /**
     * 更新订单状态
     *
     * @param updateParam
     * @return
     */
    Boolean updateStatus(OrderStatusUpdateParam updateParam);

    /**
     * 根据来源状态更新订单状态
     *
     * @param updateParam
     * @return
     */
    Boolean updateStatusByOriginStatus(OrderStatusUpdateCommandParam updateParam);


    /**
     * 更新支付方式
     *
     * @param orderCommandParam
     * @return
     */
    Boolean updatePayType(OrderCommandParam orderCommandParam);

    /**
     * 自提
     *
     * @param selfLiftParam
     * @return
     */
    Boolean selfLifting(OrderSelfLiftParam selfLiftParam);

    /**
     * 批量更新分账完成时间
     *
     * @param orderList
     * @return
     */
    Boolean batchUpdateProfitSharingFinishTime(List<OrderCommandParam> orderList);

    /**
     * 更新订单状态为完成
     *
     * @param orderIds
     * @return
     */
    int orderFinish(List<Long> orderIds);


    /**
     * 更新配送时间
     *
     * @param param
     * @return
     */
    boolean updateDeliveryTime(OrderDeliveryUpdateParam param);


    /**
     * 更新订单配送时间
     *
     * @param orderNos
     * @param deliveryTime
     * @return
     */
    Integer updateOrderDeliveryTime(List<String> orderNos, LocalDateTime deliveryTime);

    /**
     * 更新自提完成
     *
     * @param selfLiftingFinishParam
     * @return
     */
    boolean selfLiftingFinish(OrderSelfLiftingFinishParam selfLiftingFinishParam);

    OrderEntity updateDeliveryFee(Long orderId, BigDecimal newDeliveryFee, BigDecimal oriDeliveryFee);

    /**
     * 灵活更新订单费用（运费和/或手续费）
     *
     * @param orderId 订单ID
     * @param newDeliveryFee 新运费（null表示不更新运费）
     * @param oriDeliveryFee 原运费（newDeliveryFee不为null时必传）
     * @param newServiceFee 新手续费（null表示不更新手续费）
     * @param oriServiceFee 原手续费（newServiceFee不为null时必传）
     * @return 更新后的订单实体
     */
    OrderEntity updateOrderFees(Long orderId,
                               BigDecimal newDeliveryFee, BigDecimal oriDeliveryFee,
                               BigDecimal newServiceFee, BigDecimal oriServiceFee);

    /**
     * 更新订单的外部订单号
     *
     * @param orderId
     * @param newCustomerOrderId
     * @return
     */
    Boolean updateOrderCustomerOrderId(Long orderId, String newCustomerOrderId);

    Long save(OrderEntity order);


    boolean setDeliveryDatePresaleOrder(Long orderId, LocalDateTime deliveryTime);
}
