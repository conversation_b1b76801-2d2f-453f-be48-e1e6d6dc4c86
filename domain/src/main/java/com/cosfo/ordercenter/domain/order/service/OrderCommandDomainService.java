package com.cosfo.ordercenter.domain.order.service;

import com.cosfo.ordercenter.common.enums.OrderSourceEnum;
import com.cosfo.ordercenter.common.enums.OrderStatusEnum;
import com.cosfo.ordercenter.common.enums.RedisKeyEnum;
import com.cosfo.ordercenter.domain.order.entity.OrderEntity;
import com.cosfo.ordercenter.domain.order.param.command.*;
import com.cosfo.ordercenter.domain.order.repository.OrderCommandRepository;
import com.cosfo.ordercenter.domain.order.repository.OrderQueryRepository;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderCommandDomainService {

    @Resource
    private OrderCommandRepository orderCommandRepository;
    @Resource
    private OrderQueryRepository orderQueryRepository;

    public Boolean updateById(OrderCommandParam orderCommandParam) {
        return orderCommandRepository.updateById(orderCommandParam);
    }


    public Boolean updatePayType(OrderCommandParam orderCommandParam) {
        return orderCommandRepository.updatePayType(orderCommandParam);
    }


    public Boolean updateStatus(OrderStatusUpdateParam orderStatusUpdateReq) {
        return orderCommandRepository.updateStatus(orderStatusUpdateReq);
    }


    public Integer batchUpdateStatus(OrderStatusBatchUpdateParam orderStatusBatchUpdateReq) {
        return orderCommandRepository.batchUpdateStatus(orderStatusBatchUpdateReq);
    }


    public Boolean selfLifting(OrderSelfLiftParam orderSelfLiftReq) {
        return orderCommandRepository.selfLifting(orderSelfLiftReq);
    }


    public Boolean batchUpdateProfitSharingFinishTime(List<OrderCommandParam> orderCommandParams) {
        return orderCommandRepository.batchUpdateProfitSharingFinishTime(orderCommandParams);
    }


    public void updateOrderForClose(List<OrderEntity> needCloseOrder) {
        for (OrderEntity closeOrder : needCloseOrder) {
            OrderStatusUpdateCommandParam updateParam = new OrderStatusUpdateCommandParam();
            updateParam.setOrderId(closeOrder.getId());
            updateParam.setTenantId(closeOrder.getTenantId());
            updateParam.setOriginStatusList(Lists.newArrayList(OrderStatusEnum.WAIT_DELIVERY.getCode(), OrderStatusEnum.WAIT_AUDIT.getCode(), OrderStatusEnum.WAITING_DELIVERY.getCode()));
            updateParam.setStatus(OrderStatusEnum.CLOSING.getCode());
            boolean result = orderCommandRepository.updateStatusByOriginStatus(updateParam);

            if (!result) {
                throw new BizException("该订单状态不是可关单状态，关单失败");
            }
        }
        // 若是外部下发订单，更换外部单号
        updateCustomerOrderIdIfClosing(needCloseOrder);
    }


    private void updateCustomerOrderIdIfClosing(List<OrderEntity> finalNeedCloseOrder) {
        if (CollectionUtils.isEmpty(finalNeedCloseOrder)) {
            return;
        }
        List<String> orderNos = finalNeedCloseOrder.stream().map(OrderEntity::getOrderNo).collect(Collectors.toList());
        List<OrderEntity> orders = orderQueryRepository.queryByOrderNos(Lists.newArrayList(orderNos));
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        orders = orders.stream().filter(order -> OrderStatusEnum.CLOSING.getCode().equals(order.getStatus())).collect(Collectors.toList());

        String randomNumber = RandomStringUtils.randomAlphanumeric(5);
        for (OrderEntity closeOrder : orders) {
            if (Objects.isNull(closeOrder)) {
                continue;
            }
            if (!OrderSourceEnum.OPENAPI.getValue().equals(closeOrder.getOrderSource()) || StringUtils.isEmpty(closeOrder.getCustomerOrderId())) {
                continue;
            }
            if (closeOrder.getCustomerOrderId().length() > 44) {
                log.error("外部单号过长,不进行更新,:{}", closeOrder.getCustomerOrderId(), new Exception("外部单号过长"));
                continue;
            }
            String newCustomerOrderId = closeOrder.getCustomerOrderId() + RedisKeyEnum.SEPARATOR + randomNumber;
            Boolean updateResult = orderCommandRepository.updateOrderCustomerOrderId(closeOrder.getId(), newCustomerOrderId);
            if (Boolean.FALSE.equals(updateResult)) {
                throw new BizException("该订单更新外部单号失败，关单失败");
            }
        }
    }


    public Boolean auditSuccess(OrderAuditCommandParam req) {
        OrderStatusUpdateCommandParam updateParam = new OrderStatusUpdateCommandParam();
        updateParam.setOrderId(req.getOrderId());
        updateParam.setOriginStatusList(Collections.singletonList(OrderStatusEnum.WAIT_AUDIT.getCode()));
        updateParam.setStatus(OrderStatusEnum.WAITING_DELIVERY.getCode());
        return orderCommandRepository.updateStatusByOriginStatus(updateParam);
    }

    public Long saveOrder(OrderEntity orderEntity) {
        return orderCommandRepository.save(orderEntity);
    }


    public OrderEntity updateDeliveryFee(Long orderId, BigDecimal newDeliveryFee, BigDecimal oriDeliveryFee) {
        return orderCommandRepository.updateDeliveryFee(orderId, newDeliveryFee, oriDeliveryFee);
    }

    public OrderEntity updateOrderFees(Long orderId,
                                      BigDecimal newDeliveryFee, BigDecimal oriDeliveryFee,
                                      BigDecimal newServiceFee, BigDecimal oriServiceFee) {
        return orderCommandRepository.updateOrderFees(orderId, newDeliveryFee, oriDeliveryFee,
                                                      newServiceFee, oriServiceFee);
    }

    public boolean selfLiftingFinish(OrderSelfLiftingFinishParam selfLiftingFinishParam) {
        return orderCommandRepository.selfLiftingFinish(selfLiftingFinishParam);
    }

    public Integer updateOrderDeliveryTime(List<String> orderNos, LocalDateTime deliveryTime) {
        return orderCommandRepository.updateOrderDeliveryTime(orderNos, deliveryTime);
    }

    public boolean updateDeliveryTime(OrderDeliveryUpdateParam param) {
        return orderCommandRepository.updateDeliveryTime(param);
    }

    public boolean setDeliveryDatePresaleOrder(Long orderId, LocalDateTime deliveryTime) {
        return orderCommandRepository.setDeliveryDatePresaleOrder(orderId, deliveryTime);
    }

}
